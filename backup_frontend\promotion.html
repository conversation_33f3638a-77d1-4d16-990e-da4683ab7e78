<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠活动</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: white;
            display: flex;
            justify-content: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 手机端容器 */
        .mobile-container {
            width: 375px;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #ffc107;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .back-btn {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            text-decoration: none;
        }

        .back-btn img {
            width: auto;
            height: 20px;
            vertical-align: middle;
        }

        /* 标签区域 */
        .tabs-section {
            width: 100%;
            padding: 5px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
        }

        .tabs-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 5px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 2px 8px;
            margin: 0 2px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(153, 153, 153, 0.8);
            color: white;
            border: none;
            outline: none;
            line-height: 1.2;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 未点击状态 - 灰色毛玻璃 */
        .tab-item:not(.active) {
            background: rgba(153, 153, 153, 0.8);
            color: white;
        }

        /* 点击状态颜色 - 毛玻璃效果 */
        .tab-item.active.tab-1 {
            background: rgba(40, 167, 69, 0.8); /* 绿色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-2 {
            background: rgba(0, 123, 255, 0.8); /* 蓝色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-3 {
            background: rgba(220, 53, 69, 0.8); /* 红色毛玻璃 */
            color: white;
        }

        .tab-item.active.tab-4 {
            background: rgba(52, 58, 64, 0.8); /* 黑色毛玻璃 */
            color: white;
        }

        /* 悬停效果 */
        .tab-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* 开奖信息区域 */
        .lottery-info {
            width: 100%;
            padding: 5px 15px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .lottery-text {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 开奖记录按钮 */
        .record-btn {
            padding: 4px 12px;
            border: none;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #8B5CF6 0%, #FFFFFF 100%);
            color: #333;
            outline: none;
            box-shadow: 0 2px 6px rgba(139, 92, 246, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .record-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.4);
        }

        /* 号码球区域 */
        .balls-section {
            width: 100%;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .balls-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: nowrap;
        }

        .ball-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            min-width: 40px;
            flex-shrink: 0;
        }

        .ball-wrapper {
            position: relative;
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        .ball-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .ball-number {
            position: absolute;
            top: calc(50% - 2px);
            left: 50%;
            transform: translate(-50%, -50%);
            color: black;
            font-size: 16px;
            font-weight: bold;
            line-height: 1;
        }

        .ball-info {
            text-align: center;
            font-size: 10px;
            color: #666;
            font-weight: 600;
            line-height: 1.2;
        }

        /* 加号分隔符 */
        .plus-separator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #28a745;
            font-size: 20px;
            font-weight: bold;
            margin: 0 2px;
            flex-shrink: 0;
        }

        /* 开奖时间区域 */
        .draw-time-section {
            width: 100%;
            padding: 8px 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: none;
            border-right: none;
            border-top: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draw-time-text {
            font-size: 11px;
            color: #666;
            font-weight: 500;
        }

        .draw-time-text .period-number {
            color: #dc3545;
            font-weight: 600;
        }

        /* 刷新按钮 */
        .refresh-btn {
            padding: 3px 10px;
            border: none;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffc107 0%, #ffffff 100%);
            color: #333;
            outline: none;
            box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
            -webkit-tap-highlight-color: transparent;
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
        }

        /* 蓝色长条 */
        .blue-bar {
            width: 100%;
            height: 20px;
            background: #a9d5e9;
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .blue-bar-theme {
            color: #0b78ff;
            font-size: 10px;
            font-weight: 600;
            margin-right: 6px;
            white-space: nowrap;
        }

        .blue-bar-title {
            color: black;
            font-size: 10px;
            font-weight: 500;
            flex: 1;
        }

        .blue-bar-right {
            display: flex;
            align-items: center;
            font-size: 10px;
            white-space: nowrap;
        }

        .blue-bar-views {
            color: black;
            margin-right: 8px;
        }

        .view-count {
            color: red;
        }

        .blue-bar-close {
            color: black;
            text-decoration: none;
        }

        .blue-bar-close:hover {
            text-decoration: underline;
        }

        /* 内容区域 */
        .content-area {
            padding: 20px;
            min-height: calc(100vh - 60px);
        }

        .placeholder-text {
            text-align: center;
            color: #666;
            font-size: 16px;
            margin-top: 50px;
        }

        /* 真正的手机端样式 */
        @media (max-width: 767px) {
            body {
                display: block;
                background: white;
            }
            
            .mobile-container {
                width: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <!-- 手机端容器 -->
    <div class="mobile-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <a href="index.html" class="back-btn">
                <img src="images/balk.png" alt="返回">
            </a>
            <div class="page-title">优惠活动</div>
        </div>

        <!-- 标签区域 -->
        <section class="tabs-section">
            <div class="tabs-container">
                <div class="tab-item tab-1 active" onclick="selectTab(1)">私彩1</div>
                <div class="tab-item tab-2" onclick="selectTab(2)">新澳</div>
                <div class="tab-item tab-3" onclick="selectTab(3)">香港</div>
                <div class="tab-item tab-4" onclick="selectTab(4)">老澳</div>
            </div>
        </section>

        <!-- 开奖信息区域 -->
        <section class="lottery-info">
            <div class="lottery-text" id="lotteryText">
                私彩 第<span class="period-number">200</span>期开奖结果:
            </div>
            <button class="record-btn" onclick="showRecord()">开奖记录</button>
        </section>

        <!-- 号码球区域 -->
        <section class="balls-section">
            <div class="balls-container" id="ballsContainer">
                <!-- 7个号码球将通过JavaScript动态生成 -->
            </div>
        </section>

        <!-- 开奖时间区域 -->
        <section class="draw-time-section">
            <div class="draw-time-text" id="drawTimeText">
                第<span class="period-number">200</span>期开奖时间07月28日 周一21点32分
            </div>
            <button class="refresh-btn" onclick="refreshData()">刷新</button>
        </section>

        <!-- 蓝色长条 -->
        <div class="blue-bar">
            <span class="blue-bar-theme">主题:</span>
            <span class="blue-bar-title">★★★避免不必要的误会，请认真看完论坛规则★★★</span>
            <div class="blue-bar-right">
                <span class="blue-bar-views">阅读<span class="view-count">27512</span>次</span>
                <span class="blue-bar-separator"> | </span>
                <a href="javascript:void(0)" class="blue-bar-close" onclick="closePage()">关闭本页</a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <div class="placeholder-text">
                优惠活动内容待完善...
            </div>
            <!-- 这里等待添加优惠活动内容 -->
        </div>
    </div>

    <script>
        function selectTab(tabNumber) {
            // 移除所有标签的active类
            const allTabs = document.querySelectorAll('.tab-item');
            allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 给点击的标签添加active类
            const selectedTab = document.querySelector(`.tab-${tabNumber}`);
            selectedTab.classList.add('active');

            // 更新开奖信息文本
            const lotteryText = document.getElementById('lotteryText');
            const lotteryNames = {
                1: '私彩',
                2: '新澳',
                3: '香港',
                4: '老澳'
            };

            const periodNumbers = {
                1: '200',
                2: '156',
                3: '089',
                4: '234'
            };

            lotteryText.innerHTML = `${lotteryNames[tabNumber]} 第<span class="period-number">${periodNumbers[tabNumber]}</span>期开奖结果:`;

            // 更新号码球
            updateBalls(tabNumber);

            // 更新开奖时间
            updateDrawTime(tabNumber);
        }

        function updateBalls(tabNumber) {
            const ballsContainer = document.getElementById('ballsContainer');
            const ballsData = {
                1: [
                    { number: '08', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '15', zodiac: '兔', element: '木', color: 'green' },
                    { number: '23', zodiac: '猪', element: '火', color: 'red' },
                    { number: '31', zodiac: '马', element: '火', color: 'red' },
                    { number: '42', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '07', zodiac: '牛', element: '土', color: 'green' },
                    { number: '19', zodiac: '羊', element: '土', color: 'red' }
                ],
                2: [
                    { number: '03', zodiac: '兔', element: '木', color: 'green' },
                    { number: '12', zodiac: '鼠', element: '水', color: 'blue' },
                    { number: '25', zodiac: '龙', element: '土', color: 'red' },
                    { number: '34', zodiac: '虎', element: '木', color: 'green' },
                    { number: '41', zodiac: '马', element: '火', color: 'red' },
                    { number: '06', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '18', zodiac: '鸡', element: '金', color: 'blue' }
                ],
                3: [
                    { number: '01', zodiac: '猪', element: '火', color: 'red' },
                    { number: '14', zodiac: '虎', element: '木', color: 'green' },
                    { number: '27', zodiac: '兔', element: '木', color: 'green' },
                    { number: '33', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '45', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '09', zodiac: '鸡', element: '金', color: 'blue' },
                    { number: '22', zodiac: '虎', element: '木', color: 'green' }
                ],
                4: [
                    { number: '05', zodiac: '马', element: '火', color: 'red' },
                    { number: '16', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '28', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '37', zodiac: '猪', element: '火', color: 'red' },
                    { number: '44', zodiac: '猴', element: '金', color: 'blue' },
                    { number: '11', zodiac: '猪', element: '火', color: 'red' },
                    { number: '29', zodiac: '马', element: '火', color: 'red' }
                ]
            };

            const balls = ballsData[tabNumber];
            ballsContainer.innerHTML = '';

            balls.forEach((ball, index) => {
                const ballItem = document.createElement('div');
                ballItem.className = 'ball-item';
                ballItem.innerHTML = `
                    <div class="ball-wrapper">
                        <img src="images/ball-${ball.color}.png" alt="${ball.color} ball" class="ball-image">
                        <div class="ball-number">${ball.number}</div>
                    </div>
                    <div class="ball-info">${ball.zodiac}/${ball.element}</div>
                `;
                ballsContainer.appendChild(ballItem);

                // 在第6个球后添加加号分隔符
                if (index === 5) {
                    const plusSeparator = document.createElement('div');
                    plusSeparator.className = 'plus-separator';
                    plusSeparator.innerHTML = '+';
                    ballsContainer.appendChild(plusSeparator);
                }
            });
        }

        function updateDrawTime(tabNumber) {
            const drawTimeText = document.getElementById('drawTimeText');
            const drawTimes = {
                1: { period: '200', time: '开奖时间07月28日 周一21点32分' },
                2: { period: '156', time: '开奖时间07月28日 周一20点15分' },
                3: { period: '089', time: '开奖时间07月28日 周一19点45分' },
                4: { period: '234', time: '开奖时间07月28日 周一22点10分' }
            };

            const drawTime = drawTimes[tabNumber];
            drawTimeText.innerHTML = `第<span class="period-number">${drawTime.period}</span>期${drawTime.time}`;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateBalls(1); // 默认显示私彩的球
            updateDrawTime(1); // 默认显示私彩的开奖时间
        });

        function refreshData() {
            // 获取当前选中的标签
            const activeTab = document.querySelector('.tab-item.active');
            const tabNumber = activeTab.classList.contains('tab-1') ? 1 :
                            activeTab.classList.contains('tab-2') ? 2 :
                            activeTab.classList.contains('tab-3') ? 3 : 4;

            // 重新加载数据
            updateBalls(tabNumber);
            updateDrawTime(tabNumber);

            // 简单的刷新提示
            alert('数据已刷新');
        }

        function showRecord() {
            alert('开奖记录功能');
        }

        function closePage() {
            window.close();
        }
    </script>
</body>
</html>
